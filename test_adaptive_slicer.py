#!/usr/bin/env python3
"""
测试改进的自适应分块功能
"""

import os
import sys
from ply_spatial_slicer import PLYSpatialSlicer

def test_adaptive_slicing():
    """测试自适应分块功能"""
    
    # 检查是否有测试数据
    test_data_dir = "data/yanglaoyuan/ply"
    if not os.path.exists(test_data_dir):
        print(f"测试数据目录不存在: {test_data_dir}")
        return False
    
    # 查找PLY文件
    ply_files = [os.path.join(test_data_dir, f) for f in os.listdir(test_data_dir) 
                 if f.lower().endswith('.ply')]
    
    if not ply_files:
        print("未找到PLY测试文件")
        return False
    
    print(f"找到 {len(ply_files)} 个测试文件")
    
    # 测试不同的参数组合
    test_cases = [
        {"num_blocks": 8, "max_points_per_block": 8192, "description": "标准自适应分块"},
        {"num_blocks": 8, "max_points_per_block": 4096, "description": "更细粒度的自适应分块"},
        {"num_blocks": 4, "max_points_per_block": 16384, "description": "粗粒度自适应分块"},
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n=== 测试用例 {i+1}: {test_case['description']} ===")
        
        # 创建输出目录
        output_dir = f"test_output_adaptive_{i+1}"
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            # 创建切片器
            slicer = PLYSpatialSlicer(
                num_blocks=test_case["num_blocks"],
                distribution_mode="adaptive",
                max_points_per_block=test_case["max_points_per_block"]
            )
            
            # 执行切片
            slice_info = slicer.slice_files(ply_files, output_dir)
            
            # 分析结果
            total_blocks = len(slice_info["blocks"])
            non_empty_blocks = len([b for b in slice_info["blocks"].values() if b["total_points"] > 0])
            max_points_in_block = max([b["total_points"] for b in slice_info["blocks"].values()])
            min_points_in_block = min([b["total_points"] for b in slice_info["blocks"].values() if b["total_points"] > 0])
            
            print(f"  总块数: {total_blocks}")
            print(f"  非空块数: {non_empty_blocks}")
            print(f"  最大块点数: {max_points_in_block}")
            print(f"  最小块点数: {min_points_in_block}")
            print(f"  总点数: {slice_info['metadata']['total_points']:,}")
            
            # 检查是否超过最大点数限制
            oversized_blocks = [b for b in slice_info["blocks"].values() 
                               if b["total_points"] > test_case["max_points_per_block"]]
            
            if oversized_blocks:
                print(f"  警告: {len(oversized_blocks)} 个块超过最大点数限制")
                for block in oversized_blocks[:3]:  # 只显示前3个
                    print(f"    块 {block['block_id']}: {block['total_points']} 点")
            else:
                print(f"  ✓ 所有块都在点数限制内")
                
        except Exception as e:
            print(f"  错误: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

if __name__ == "__main__":
    print("测试改进的自适应分块功能...")
    success = test_adaptive_slicing()
    
    if success:
        print("\n✓ 所有测试通过!")
    else:
        print("\n✗ 测试失败!")
        sys.exit(1) 