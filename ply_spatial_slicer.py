#!/usr/bin/env python3
"""
PLY格式高斯切片工具
基于3D空间分布将PLY格式的高斯文件切分为指定数量的块
支持均匀空间分布和自适应分布两种模式

作者：基于现有splat-3dtiles代码开发
"""

import argparse
import json
import os
import time
import math
from multiprocessing import Pool, cpu_count, Manager
from typing import Dict, Tuple, List, Optional
from collections import defaultdict

import numpy as np
from tqdm import tqdm
from common import read_ply_file, write_ply_file, get_point_num
from point import Point, compute_box


class SpatialBlock:
    """空间块定义"""
    def __init__(self, block_id: int, bounds: Tuple[float, float, float, float, float, float]):
        self.block_id = block_id
        self.bounds = bounds  # (min_x, min_y, min_z, max_x, max_y, max_z)
        self.points = []
        self.point_count = 0
    
    def contains_point(self, position: Tuple[float, float, float]) -> bool:
        """检查点是否在当前块内"""
        x, y, z = position
        min_x, min_y, min_z, max_x, max_y, max_z = self.bounds
        return (min_x <= x < max_x and 
                min_y <= y < max_y and 
                min_z <= z < max_z)
    
    def add_point(self, point: Point):
        """添加点到当前块"""
        self.points.append(point)
        self.point_count += 1
    
    def get_filename(self, input_filename: str) -> str:
        """生成块文件名"""
        base_name = os.path.splitext(input_filename)[0]
        return f"{base_name}_block_{self.block_id:04d}.ply"
    
    def get_center(self) -> Tuple[float, float, float]:
        """获取块的中心点"""
        min_x, min_y, min_z, max_x, max_y, max_z = self.bounds
        return ((min_x + max_x) / 2, (min_y + max_y) / 2, (min_z + max_z) / 2)
    
    def get_size(self) -> Tuple[float, float, float]:
        """获取块的尺寸"""
        min_x, min_y, min_z, max_x, max_y, max_z = self.bounds
        return (max_x - min_x, max_y - min_y, max_z - min_z)


class PLYSpatialSlicer:
    """PLY空间切片器"""
    
    def __init__(self, num_blocks: int, distribution_mode: str = "uniform", max_points_per_block: int = 8192, min_points_per_block: int = 8192):
        """
        初始化切片器
        
        Args:
            num_blocks: 目标块数量
            distribution_mode: 分布模式 ("uniform": 均匀分布, "adaptive": 自适应分布)
            max_points_per_block: 自适应模式下每个块的最大点数
            min_points_per_block: 自适应模式下每个块的最小点数
        """
        self.num_blocks = num_blocks
        self.distribution_mode = distribution_mode
        self.max_points_per_block = max_points_per_block
        self.min_points_per_block = min_points_per_block
        self.blocks = []
        self.global_bounds = None
    
    def calculate_bounds(self, input_files: List[str]) -> Tuple[float, float, float, float, float, float]:
        """计算所有输入文件的全局边界"""
        print("正在计算全局空间边界...")
        
        min_x = min_y = min_z = float('inf')
        max_x = max_y = max_z = float('-inf')
        total_points = 0
        
        for input_file in tqdm(input_files, desc="扫描文件边界"):
            try:
                points = read_ply_file(input_file)
                total_points += len(points)
                
                for point in points:
                    x, y, z = point.position
                    min_x = min(min_x, x)
                    min_y = min(min_y, y)
                    min_z = min(min_z, z)
                    max_x = max(max_x, x)
                    max_y = max(max_y, y)
                    max_z = max(max_z, z)
            except Exception as e:
                print(f"警告: 无法读取文件 {input_file}: {e}")
                continue
        
        bounds = (min_x, min_y, min_z, max_x, max_y, max_z)
        print(f"总点数: {total_points:,}")
        print(f"全局边界: X[{min_x:.3f}, {max_x:.3f}] Y[{min_y:.3f}, {max_y:.3f}] Z[{min_z:.3f}, {max_z:.3f}]")
        
        return bounds
    
    def create_uniform_blocks(self, bounds: Tuple[float, float, float, float, float, float]):
        """创建均匀分布的空间块"""
        min_x, min_y, min_z, max_x, max_y, max_z = bounds
        
        # 计算空间尺寸
        size_x = max_x - min_x
        size_y = max_y - min_y
        size_z = max_z - min_z
        
        # 计算最佳的3D网格分布
        # 尝试找到最接近立方体的分割方式
        best_diff = float('inf')
        best_division = (1, 1, self.num_blocks)
        
        for nx in range(1, self.num_blocks + 1):
            if self.num_blocks % nx != 0:
                continue
            remaining = self.num_blocks // nx
            
            for ny in range(1, remaining + 1):
                if remaining % ny != 0:
                    continue
                nz = remaining // ny
                
                # 计算每个块的尺寸
                block_size_x = size_x / nx
                block_size_y = size_y / ny
                block_size_z = size_z / nz
                
                # 计算尺寸差异（越接近立方体越好）
                sizes = [block_size_x, block_size_y, block_size_z]
                diff = max(sizes) - min(sizes)
                
                if diff < best_diff:
                    best_diff = diff
                    best_division = (nx, ny, nz)
        
        nx, ny, nz = best_division
        print(f"使用网格分割: {nx} x {ny} x {nz} = {nx*ny*nz} 块")
        
        # 创建空间块
        self.blocks = []
        block_id = 0
        
        for i in range(nx):
            for j in range(ny):
                for k in range(nz):
                    # 计算当前块的边界
                    block_min_x = min_x + i * size_x / nx
                    block_max_x = min_x + (i + 1) * size_x / nx
                    block_min_y = min_y + j * size_y / ny
                    block_max_y = min_y + (j + 1) * size_y / ny
                    block_min_z = min_z + k * size_z / nz
                    block_max_z = min_z + (k + 1) * size_z / nz
                    
                    # 确保最后一个块包含边界点
                    if i == nx - 1:
                        block_max_x = max_x + 1e-6
                    if j == ny - 1:
                        block_max_y = max_y + 1e-6
                    if k == nz - 1:
                        block_max_z = max_z + 1e-6
                    
                    block_bounds = (block_min_x, block_min_y, block_min_z, 
                                  block_max_x, block_max_y, block_max_z)
                    
                    block = SpatialBlock(block_id, block_bounds)
                    self.blocks.append(block)
                    block_id += 1
    
    def create_adaptive_blocks(self, input_files: List[str], bounds: Tuple[float, float, float, float, float, float], max_points_per_block: int = 8192):
        """创建自适应分布的空间块（基于点密度和块大小）"""
        print("创建自适应空间块...")
        
        # 首先创建均匀分布的基础块
        print("创建基础均匀分布块...")
        self.create_uniform_blocks(bounds)
        
        # 统计每个块的点数
        print("统计各块的点数分布...")
        block_point_counts = {}
        
        for input_file in tqdm(input_files, desc="分析块密度"):
            try:
                points = read_ply_file(input_file)
                for point in points:
                    for block in self.blocks:
                        if block.contains_point(point.position):
                            if block.block_id not in block_point_counts:
                                block_point_counts[block.block_id] = 0
                            block_point_counts[block.block_id] += 1
                            break
            except Exception as e:
                print(f"警告: 无法读取文件 {input_file}: {e}")
                continue
        
        # 找出需要进一步细分的块
        blocks_to_subdivide = []
        for block_id, point_count in block_point_counts.items():
            if point_count > max_points_per_block:
                blocks_to_subdivide.append((block_id, point_count))
        
        if not blocks_to_subdivide:
            print("所有块的点数都在合理范围内，无需进一步细分")
        else:
            print(f"发现 {len(blocks_to_subdivide)} 个块需要进一步细分:")
            for block_id, point_count in blocks_to_subdivide:
                print(f"  块 {block_id}: {point_count} 个点")
            
            # 对需要细分的块进行进一步分割
            new_blocks = []
            next_block_id = len(self.blocks)
            
            for block in self.blocks:
                if block.block_id in [bid for bid, _ in blocks_to_subdivide]:
                    # 细分这个大块
                    subdivided_blocks = self._subdivide_block(block, next_block_id, max_points_per_block)
                    new_blocks.extend(subdivided_blocks)
                    next_block_id += len(subdivided_blocks)
                else:
                    # 保持原块不变
                    new_blocks.append(block)
            
            self.blocks = new_blocks
        
        print(f"自适应分块完成，总共 {len(self.blocks)} 个块")
    
    def _subdivide_block(self, block: SpatialBlock, start_block_id: int, max_points_per_block: int) -> List[SpatialBlock]:
        """细分一个空间块"""
        min_x, min_y, min_z, max_x, max_y, max_z = block.bounds
        
        # 计算块的尺寸
        size_x = max_x - min_x
        size_y = max_y - min_y
        size_z = max_z - min_z
        
        # 根据块的形状决定分割方式
        # 优先沿着最长的维度进行分割
        dimensions = [(size_x, 'x'), (size_y, 'y'), (size_z, 'z')]
        dimensions.sort(reverse=True)  # 按尺寸降序排列
        
        # 选择分割方式：2x2x2 或 2x2x1 或 2x1x1
        if dimensions[0][0] > dimensions[1][0] * 2:
            # 最长维度明显更长，优先沿该维度分割
            if dimensions[0][1] == 'x':
                divisions = (2, 1, 1)
            elif dimensions[0][1] == 'y':
                divisions = (1, 2, 1)
            else:
                divisions = (1, 1, 2)
        else:
            # 维度相对均匀，使用2x2x1或2x2x2分割
            if dimensions[2][0] < dimensions[0][0] * 0.5:
                divisions = (2, 2, 1)
            else:
                divisions = (2, 2, 2)
        
        nx, ny, nz = divisions
        subdivided_blocks = []
        
        for i in range(nx):
            for j in range(ny):
                for k in range(nz):
                    # 计算子块的边界
                    sub_min_x = min_x + i * size_x / nx
                    sub_max_x = min_x + (i + 1) * size_x / nx
                    sub_min_y = min_y + j * size_y / ny
                    sub_max_y = min_y + (j + 1) * size_y / ny
                    sub_min_z = min_z + k * size_z / nz
                    sub_max_z = min_z + (k + 1) * size_z / nz
                    
                    # 确保最后一个子块包含边界点
                    if i == nx - 1:
                        sub_max_x = max_x + 1e-6
                    if j == ny - 1:
                        sub_max_y = max_y + 1e-6
                    if k == nz - 1:
                        sub_max_z = max_z + 1e-6
                    
                    sub_bounds = (sub_min_x, sub_min_y, sub_min_z, 
                                sub_max_x, sub_max_y, sub_max_z)
                    
                    sub_block = SpatialBlock(start_block_id + len(subdivided_blocks), sub_bounds)
                    subdivided_blocks.append(sub_block)
        
        print(f"  块 {block.block_id} 细分为 {len(subdivided_blocks)} 个子块")
        return subdivided_blocks
    
    def _merge_small_blocks(self, block_point_counts: Dict[int, int]):
        """合并过小的块"""
        if self.min_points_per_block <= 0:
            return
        
        print("检查并合并过小的块...")
        
        max_iterations = 10  # 最大迭代次数，防止无限循环
        iteration = 0
        
        while iteration < max_iterations:
            iteration += 1
            print(f"第 {iteration} 次合并迭代...")
            
            # 重新统计每个块的点数
            block_point_counts = {}
            for block in self.blocks:
                block_point_counts[block.block_id] = 0
            
            # 这里需要重新统计点数，但由于没有input_files参数，我们暂时跳过
            # 在实际使用中，这个方法应该被_ensure_min_points_per_block调用
            
            # 找出过小的块
            small_blocks = []
            for block in self.blocks:
                point_count = block_point_counts.get(block.block_id, 0)
                if point_count < self.min_points_per_block and point_count > 0:
                    small_blocks.append((block, point_count))
            
            if not small_blocks:
                print("没有发现过小的块，合并完成")
                break
            
            print(f"发现 {len(small_blocks)} 个过小的块需要合并:")
            for block, point_count in small_blocks:
                print(f"  块 {block.block_id}: {point_count} 个点")
            
            # 按点数排序，优先合并最小的块
            small_blocks.sort(key=lambda x: x[1])
            
            # 合并过小的块到相邻的块中
            merged_blocks = set()
            merged_this_iteration = False
            
            for block, point_count in small_blocks:
                if block.block_id in merged_blocks:
                    continue
                
                # 找到最佳的合并目标块
                target_block = self._find_best_merge_target(block, merged_blocks)
                
                if target_block is not None:
                    # 合并两个块
                    merged_bounds = self._merge_block_bounds(block.bounds, target_block.bounds)
                    merged_block = SpatialBlock(target_block.block_id, merged_bounds)
                    
                    # 更新块列表
                    new_blocks = []
                    for b in self.blocks:
                        if b.block_id == block.block_id:
                            continue  # 跳过被合并的块
                        elif b.block_id == target_block.block_id:
                            new_blocks.append(merged_block)  # 使用合并后的块
                        else:
                            new_blocks.append(b)
                    
                    self.blocks = new_blocks
                    merged_blocks.add(block.block_id)
                    merged_blocks.add(target_block.block_id)
                    merged_this_iteration = True
                    
                    print(f"  合并块 {block.block_id} ({point_count} 点) 到块 {target_block.block_id}")
            
            if not merged_this_iteration:
                print("无法找到合适的合并目标，停止合并")
                break
        
        print(f"合并完成，剩余 {len(self.blocks)} 个块")
    
    def _merge_small_blocks_with_counts(self, block_point_counts: Dict[int, int]):
        """合并过小的块（使用提供的点数统计）"""
        if self.min_points_per_block <= 0:
            return
        
        print("合并过小的块...")
        
        # 找出过小的块
        small_blocks = []
        for block in self.blocks:
            point_count = block_point_counts.get(block.block_id, 0)
            if point_count < self.min_points_per_block and point_count > 0:
                small_blocks.append((block, point_count))
        
        if not small_blocks:
            print("没有发现过小的块，无需合并")
            return
        
        print(f"发现 {len(small_blocks)} 个过小的块需要合并:")
        for block, point_count in small_blocks:
            print(f"  块 {block.block_id}: {point_count} 个点")
        
        # 按点数排序，优先合并最小的块
        small_blocks.sort(key=lambda x: x[1])
        
        # 合并过小的块到相邻的块中
        merged_blocks = set()
        merged_this_iteration = False
        
        for block, point_count in small_blocks:
            if block.block_id in merged_blocks:
                continue
            
            # 找到最佳的合并目标块
            target_block = self._find_best_merge_target(block, merged_blocks)
            
            if target_block is not None:
                # 合并两个块
                merged_bounds = self._merge_block_bounds(block.bounds, target_block.bounds)
                merged_block = SpatialBlock(target_block.block_id, merged_bounds)
                
                # 更新块列表
                new_blocks = []
                for b in self.blocks:
                    if b.block_id == block.block_id:
                        continue  # 跳过被合并的块
                    elif b.block_id == target_block.block_id:
                        new_blocks.append(merged_block)  # 使用合并后的块
                    else:
                        new_blocks.append(b)
                
                self.blocks = new_blocks
                merged_blocks.add(block.block_id)
                merged_blocks.add(target_block.block_id)
                merged_this_iteration = True
                
                print(f"  合并块 {block.block_id} ({point_count} 点) 到块 {target_block.block_id}")
        
        if not merged_this_iteration:
            print("无法找到合适的合并目标，尝试强制合并...")
            # 如果常规合并失败，尝试强制合并到最近的块
            self._force_merge_remaining_small_blocks(block_point_counts)
    
    def _force_merge_remaining_small_blocks(self, block_point_counts: Dict[int, int]):
        """强制合并剩余的小块"""
        print("执行强制合并...")
        
        # 找出所有过小的块
        small_blocks = []
        for block in self.blocks:
            point_count = block_point_counts.get(block.block_id, 0)
            if point_count < self.min_points_per_block and point_count > 0:
                small_blocks.append((block, point_count))
        
        if not small_blocks:
            return
        
        print(f"强制合并 {len(small_blocks)} 个剩余的小块:")
        for block, point_count in small_blocks:
            print(f"  块 {block.block_id}: {point_count} 个点")
        
        # 按点数排序，优先处理最小的块
        small_blocks.sort(key=lambda x: x[1])
        
        for block, point_count in small_blocks:
            # 找到最近的块进行合并
            target_block = self._find_closest_block_for_merge(block)
            if target_block is not None:
                # 合并两个块
                merged_bounds = self._merge_block_bounds(block.bounds, target_block.bounds)
                merged_block = SpatialBlock(target_block.block_id, merged_bounds)
                
                # 更新块列表
                new_blocks = []
                for b in self.blocks:
                    if b.block_id == block.block_id:
                        continue  # 跳过被合并的块
                    elif b.block_id == target_block.block_id:
                        new_blocks.append(merged_block)  # 使用合并后的块
                    else:
                        new_blocks.append(b)
                
                self.blocks = new_blocks
                print(f"  强制合并块 {block.block_id} ({point_count} 点) 到块 {target_block.block_id}")
    
    def _find_closest_block_for_merge(self, small_block: SpatialBlock) -> Optional[SpatialBlock]:
        """找到距离最近的块进行合并"""
        best_target = None
        min_distance = float('inf')
        
        small_center = small_block.get_center()
        
        for block in self.blocks:
            if block.block_id == small_block.block_id:
                continue
            
            # 计算两个块中心之间的距离
            target_center = block.get_center()
            distance = ((small_center[0] - target_center[0])**2 + 
                       (small_center[1] - target_center[1])**2 + 
                       (small_center[2] - target_center[2])**2)**0.5
            
            if distance < min_distance:
                min_distance = distance
                best_target = block
        
        return best_target
    
    def _find_best_merge_target(self, small_block: SpatialBlock, merged_blocks: set) -> Optional[SpatialBlock]:
        """找到最佳的合并目标块"""
        best_target = None
        min_distance = float('inf')
        
        small_center = small_block.get_center()
        
        # 首先尝试找到相邻的块
        for block in self.blocks:
            if (block.block_id == small_block.block_id or 
                block.block_id in merged_blocks):
                continue
            
            # 计算两个块中心之间的距离
            target_center = block.get_center()
            distance = ((small_center[0] - target_center[0])**2 + 
                       (small_center[1] - target_center[1])**2 + 
                       (small_center[2] - target_center[2])**2)**0.5
            
            # 优先选择相邻的块
            if self._are_blocks_adjacent(small_block, block):
                if distance < min_distance:
                    min_distance = distance
                    best_target = block
        
        # 如果没有找到相邻的块，则选择最近的块
        if best_target is None:
            print(f"  块 {small_block.block_id} 没有找到相邻的块，寻找最近的块...")
            for block in self.blocks:
                if (block.block_id == small_block.block_id or 
                    block.block_id in merged_blocks):
                    continue
                
                # 计算两个块中心之间的距离
                target_center = block.get_center()
                distance = ((small_center[0] - target_center[0])**2 + 
                           (small_center[1] - target_center[1])**2 + 
                           (small_center[2] - target_center[2])**2)**0.5
                
                if distance < min_distance:
                    min_distance = distance
                    best_target = block
        
        return best_target
    
    def _are_blocks_adjacent(self, block1: SpatialBlock, block2: SpatialBlock) -> bool:
        """检查两个块是否相邻"""
        min_x1, min_y1, min_z1, max_x1, max_y1, max_z1 = block1.bounds
        min_x2, min_y2, min_z2, max_x2, max_y2, max_z2 = block2.bounds
        
        # 检查是否在X、Y、Z轴上相邻
        x_adjacent = (abs(max_x1 - min_x2) < 1e-6) or (abs(max_x2 - min_x1) < 1e-6)
        y_adjacent = (abs(max_y1 - min_y2) < 1e-6) or (abs(max_y2 - min_y1) < 1e-6)
        z_adjacent = (abs(max_z1 - min_z2) < 1e-6) or (abs(max_z2 - min_z1) < 1e-6)
        
        # 检查是否有重叠
        x_overlap = not (max_x1 <= min_x2 or max_x2 <= min_x1)
        y_overlap = not (max_y1 <= min_y2 or max_y2 <= min_y1)
        z_overlap = not (max_z1 <= min_z2 or max_z2 <= min_z1)
        
        # 相邻：在至少一个轴上相邻，在其他轴上重叠
        return ((x_adjacent and y_overlap and z_overlap) or
                (y_adjacent and x_overlap and z_overlap) or
                (z_adjacent and x_overlap and y_overlap))
    
    def _merge_block_bounds(self, bounds1: Tuple[float, float, float, float, float, float], 
                           bounds2: Tuple[float, float, float, float, float, float]) -> Tuple[float, float, float, float, float, float]:
        """合并两个块的边界"""
        min_x1, min_y1, min_z1, max_x1, max_y1, max_z1 = bounds1
        min_x2, min_y2, min_z2, max_x2, max_y2, max_z2 = bounds2
        
        merged_min_x = min(min_x1, min_x2)
        merged_min_y = min(min_y1, min_y2)
        merged_min_z = min(min_z1, min_z2)
        merged_max_x = max(max_x1, max_x2)
        merged_max_y = max(max_y1, max_y2)
        merged_max_z = max(max_z1, max_z2)
        
        return (merged_min_x, merged_min_y, merged_min_z, merged_max_x, merged_max_y, merged_max_z)
    
    def _ensure_min_points_per_block(self, input_files: List[str]):
        """确保所有块都满足最小点数要求"""
        if self.min_points_per_block <= 0:
            return
        
        print("检查并确保所有块满足最小点数要求...")
        
        max_iterations = 10  # 最大迭代次数，防止无限循环
        iteration = 0
        
        while iteration < max_iterations:
            iteration += 1
            print(f"第 {iteration} 次检查最小点数要求...")
            
            # 统计每个块的点数
            block_point_counts = {}
            for input_file in tqdm(input_files, desc="统计块点数"):
                try:
                    points = read_ply_file(input_file)
                    for point in points:
                        for block in self.blocks:
                            if block.contains_point(point.position):
                                if block.block_id not in block_point_counts:
                                    block_point_counts[block.block_id] = 0
                                block_point_counts[block.block_id] += 1
                                break
                except Exception as e:
                    print(f"警告: 无法读取文件 {input_file}: {e}")
                    continue
            
            # 检查是否有过小的块
            small_blocks = []
            for block in self.blocks:
                point_count = block_point_counts.get(block.block_id, 0)
                if point_count < self.min_points_per_block and point_count > 0:
                    small_blocks.append((block, point_count))
            
            if not small_blocks:
                print("所有块都满足最小点数要求")
                break
            
            print(f"发现 {len(small_blocks)} 个块点数不足 {self.min_points_per_block}:")
            for block, point_count in small_blocks:
                print(f"  块 {block.block_id}: {point_count} 个点")
            
            # 合并过小的块
            self._merge_small_blocks_with_counts(block_point_counts)
            
            # 检查是否还有过小的块
            remaining_small_blocks = []
            for block in self.blocks:
                point_count = block_point_counts.get(block.block_id, 0)
                if point_count < self.min_points_per_block and point_count > 0:
                    remaining_small_blocks.append((block, point_count))
            
            if not remaining_small_blocks:
                print("所有块都满足最小点数要求")
                break
            else:
                print(f"仍有 {len(remaining_small_blocks)} 个块不满足要求，继续迭代...")
        
        if iteration >= max_iterations:
            print("警告: 达到最大迭代次数，可能仍有块不满足最小点数要求")
            # 最后尝试强制合并所有剩余的小块
            self._force_merge_all_remaining_small_blocks(input_files)
    
    def _force_merge_all_remaining_small_blocks(self, input_files: List[str]):
        """强制合并所有剩余的小块"""
        print("执行最终强制合并...")
        
        # 重新统计点数
        block_point_counts = {}
        for input_file in tqdm(input_files, desc="最终统计块点数"):
            try:
                points = read_ply_file(input_file)
                for point in points:
                    for block in self.blocks:
                        if block.contains_point(point.position):
                            if block.block_id not in block_point_counts:
                                block_point_counts[block.block_id] = 0
                            block_point_counts[block.block_id] += 1
                            break
            except Exception as e:
                print(f"警告: 无法读取文件 {input_file}: {e}")
                continue
        
        # 找出所有过小的块
        small_blocks = []
        for block in self.blocks:
            point_count = block_point_counts.get(block.block_id, 0)
            if point_count < self.min_points_per_block and point_count > 0:
                small_blocks.append((block, point_count))
        
        if not small_blocks:
            print("没有剩余的小块需要合并")
            return
        
        print(f"最终强制合并 {len(small_blocks)} 个小块:")
        for block, point_count in small_blocks:
            print(f"  块 {block.block_id}: {point_count} 个点")
        
        # 按点数排序，优先处理最小的块
        small_blocks.sort(key=lambda x: x[1])
        
        for block, point_count in small_blocks:
            # 找到最近的块进行合并
            target_block = self._find_closest_block_for_merge(block)
            if target_block is not None:
                # 合并两个块
                merged_bounds = self._merge_block_bounds(block.bounds, target_block.bounds)
                merged_block = SpatialBlock(target_block.block_id, merged_bounds)
                
                # 更新块列表
                new_blocks = []
                for b in self.blocks:
                    if b.block_id == block.block_id:
                        continue  # 跳过被合并的块
                    elif b.block_id == target_block.block_id:
                        new_blocks.append(merged_block)  # 使用合并后的块
                    else:
                        new_blocks.append(b)
                
                self.blocks = new_blocks
                print(f"  最终合并块 {block.block_id} ({point_count} 点) 到块 {target_block.block_id}")
    
    def _ensure_max_points_per_block(self, input_files: List[str]):
        """确保所有块都满足最大点数要求"""
        if self.max_points_per_block <= 0:
            return
        
        print("检查并确保所有块满足最大点数要求...")
        
        # 统计每个块的点数
        block_point_counts = {}
        for input_file in tqdm(input_files, desc="统计块点数"):
            try:
                points = read_ply_file(input_file)
                for point in points:
                    for block in self.blocks:
                        if block.contains_point(point.position):
                            if block.block_id not in block_point_counts:
                                block_point_counts[block.block_id] = 0
                            block_point_counts[block.block_id] += 1
                            break
            except Exception as e:
                print(f"警告: 无法读取文件 {input_file}: {e}")
                continue
        
        # 检查是否有过大的块
        large_blocks = []
        for block in self.blocks:
            point_count = block_point_counts.get(block.block_id, 0)
            if point_count > self.max_points_per_block:
                large_blocks.append((block, point_count))
        
        if not large_blocks:
            print("所有块都满足最大点数要求")
            return
        
        print(f"发现 {len(large_blocks)} 个块点数超过 {self.max_points_per_block}:")
        for block, point_count in large_blocks:
            print(f"  块 {block.block_id}: {point_count} 个点")
        
        # 细分过大的块
        for block, point_count in large_blocks:
            print(f"细分块 {block.block_id} ({point_count} 个点)")
            subdivided_blocks = self._subdivide_block(block, len(self.blocks), self.max_points_per_block)
            
            # 更新块列表
            new_blocks = []
            for b in self.blocks:
                if b.block_id == block.block_id:
                    new_blocks.extend(subdivided_blocks)  # 替换为细分后的块
                else:
                    new_blocks.append(b)
            
            self.blocks = new_blocks
    
    def assign_points_to_blocks(self, input_file: str) -> Dict[int, List[Point]]:
        """将文件中的点分配到对应的空间块"""
        try:
            points = read_ply_file(input_file)
        except Exception as e:
            print(f"错误: 无法读取PLY文件 {input_file}: {e}")
            return {}
        
        block_points = defaultdict(list)
        unassigned_points = []
        
        for point in points:
            assigned = False
            for block in self.blocks:
                if block.contains_point(point.position):
                    block_points[block.block_id].append(point)
                    assigned = True
                    break
            
            if not assigned:
                unassigned_points.append(point)
        
        # 处理未分配的点（分配给最近的块）
        if unassigned_points:
            print(f"警告: {len(unassigned_points)} 个点未能分配到任何块，将分配给最近的块")
            for point in unassigned_points:
                closest_block_id = self._find_closest_block(point.position)
                block_points[closest_block_id].append(point)
        
        return dict(block_points)
    
    def _find_closest_block(self, position: Tuple[float, float, float]) -> int:
        """找到距离给定位置最近的块"""
        x, y, z = position
        min_distance = float('inf')
        closest_block_id = 0
        
        for block in self.blocks:
            center = block.get_center()
            distance = ((x - center[0])**2 + (y - center[1])**2 + (z - center[2])**2)**0.5
            if distance < min_distance:
                min_distance = distance
                closest_block_id = block.block_id
        
        return closest_block_id
    
    def slice_files(self, input_files: List[str], output_dir: str) -> Dict:
        """对输入文件进行空间切片"""
        # 计算全局边界
        self.global_bounds = self.calculate_bounds(input_files)
        
        # 创建空间块
        if self.distribution_mode == "adaptive":
            self.create_adaptive_blocks(input_files, self.global_bounds, self.max_points_per_block)
        else:
            self.create_uniform_blocks(self.global_bounds)
        
        # 在所有模式下都检查并确保块满足点数要求
        # if self.max_points_per_block > 0:
        #     self._ensure_max_points_per_block(input_files)
        
        # if self.min_points_per_block > 0:
        #     self._ensure_min_points_per_block(input_files)
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建块ID到块对象的映射
        block_id_to_block = {block.block_id: block for block in self.blocks}
        
        # 处理每个输入文件
        all_block_info = {}
        total_points = 0
        
        for input_file in tqdm(input_files, desc="处理文件"):
            input_filename = os.path.basename(input_file)
            block_points = self.assign_points_to_blocks(input_file)
            
            # 写入每个块的文件
            for block_id, points in block_points.items():
                if not points:
                    continue
                
                if block_id not in block_id_to_block:
                    print(f"警告: 块ID {block_id} 不存在，跳过")
                    continue
                    
                block = block_id_to_block[block_id]
                output_filename = block.get_filename(input_filename)
                output_path = os.path.join(output_dir, output_filename)
                
                # 写入PLY文件
                write_ply_file(output_path, points)
                
                # 记录块信息
                if block_id not in all_block_info:
                    all_block_info[block_id] = {
                        "block_id": block_id,
                        "bounds": block.bounds,
                        "center": block.get_center(),
                        "size": block.get_size(),
                        "files": [],
                        "total_points": 0
                    }
                
                all_block_info[block_id]["files"].append({
                    "filename": output_filename,
                    "source_file": input_filename,
                    "point_count": len(points)
                })
                all_block_info[block_id]["total_points"] += len(points)
                total_points += len(points)
        
        # 生成切片信息JSON
        slice_info = {
            "metadata": {
                "version": "1.0",
                "generator": "PLY Spatial Slicer",
                "created": time.strftime("%Y-%m-%d %H:%M:%S"),
                "input_files": [os.path.basename(f) for f in input_files],
                "num_blocks": self.num_blocks,
                "distribution_mode": self.distribution_mode,
                "total_points": total_points
            },
            "global_bounds": {
                "min": list(self.global_bounds[:3]),
                "max": list(self.global_bounds[3:]),
                "center": [
                    (self.global_bounds[0] + self.global_bounds[3]) / 2,
                    (self.global_bounds[1] + self.global_bounds[4]) / 2,
                    (self.global_bounds[2] + self.global_bounds[5]) / 2
                ],
                "size": [
                    self.global_bounds[3] - self.global_bounds[0],
                    self.global_bounds[4] - self.global_bounds[1],
                    self.global_bounds[5] - self.global_bounds[2]
                ]
            },
            "blocks": all_block_info
        }
        
        # 写入JSON文件
        json_path = os.path.join(output_dir, "spatial_slice_info.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(slice_info, f, indent=2, ensure_ascii=False)
        
        return slice_info


def main():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(
        description="PLY格式高斯切片工具 - 按空间分布将PLY文件切分为指定数量的块",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 将PLY文件切分为8个空间块（均匀分布）
  python ply_spatial_slicer.py -i input.ply -o output_blocks/ -n 8

  # 将目录中的PLY文件切分为16个空间块（自适应分布）
  python ply_spatial_slicer.py -i input_dir/ -o output_blocks/ -n 16 --mode adaptive

  # 自适应分布，每个块最多4096个点，最少2048个点
  python ply_spatial_slicer.py -i input_dir/ -o output_blocks/ -n 16 --mode adaptive --max-points-per-block 4096 --min-points-per-block 2048

  # 处理多个PLY文件，切分为27个块
  python ply_spatial_slicer.py -i "*.ply" -o output_blocks/ -n 27
  
  # 均匀分布，确保每个块至少有2048个点
  python ply_spatial_slicer.py -i input_dir/ -o output_blocks/ -n 8 --min-points-per-block 2048
        """
    )

    parser.add_argument("-i", "--input", required=True,
                       help="输入PLY文件或包含PLY文件的目录")
    parser.add_argument("-o", "--output", required=True,
                       help="输出目录，切片后的文件将保存在此目录")
    parser.add_argument("-n", "--num-blocks", type=int, required=True,
                       help="目标块数量")
    parser.add_argument("--mode", choices=["uniform", "adaptive"], default="uniform",
                       help="分布模式: uniform=均匀分布(默认), adaptive=自适应分布")
    parser.add_argument("--max-points-per-block", type=int, default=8192,
                       help="自适应模式下每个块的最大点数(默认: 8192)")
    parser.add_argument("--min-points-per-block", type=int, default=8192,
                       help="每个块的最小点数，在所有模式下都生效(默认: 8192)")
    parser.add_argument("--version", action="version", version="PLY Spatial Slicer 1.0")

    args = parser.parse_args()

    # 解析参数
    input_path = args.input
    output_dir = args.output
    num_blocks = args.num_blocks
    distribution_mode = args.mode
    max_points_per_block = args.max_points_per_block
    min_points_per_block = args.min_points_per_block

    # 验证输入
    if not os.path.exists(input_path):
        print(f"错误: 输入路径不存在: {input_path}")
        return 1

    if num_blocks <= 0:
        print(f"错误: 块数量必须大于0: {num_blocks}")
        return 1

    # 获取输入文件列表
    input_files = []
    if os.path.isfile(input_path):
        if input_path.lower().endswith('.ply'):
            input_files = [input_path]
        else:
            print(f"错误: {input_path} 不是PLY文件")
            return 1
    elif os.path.isdir(input_path):
        input_files = [os.path.join(input_path, f) for f in os.listdir(input_path) 
                      if f.lower().endswith('.ply')]
    else:
        print(f"错误: {input_path} 不存在")
        return 1

    if not input_files:
        print("未找到PLY文件")
        return 1

    print(f"找到 {len(input_files)} 个PLY文件")
    print(f"目标块数量: {num_blocks}")
    print(f"分布模式: {distribution_mode}")
    if distribution_mode == "adaptive":
        print(f"每个块最大点数: {max_points_per_block}")
    if min_points_per_block > 0:
        print(f"每个块最小点数: {min_points_per_block}")

    # 执行切片
    try:
        start_time = time.time()
        
        slicer = PLYSpatialSlicer(num_blocks, distribution_mode, max_points_per_block, min_points_per_block)
        slice_info = slicer.slice_files(input_files, output_dir)
        
        end_time = time.time()
        
        # 统计结果
        output_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.ply')]
        non_empty_blocks = len([b for b in slice_info["blocks"].values() if b["total_points"] > 0])
        
        print(f"\n切片完成!")
        print(f"输入文件: {len(input_files)} 个")
        print(f"输出块文件: {len(output_files)} 个")
        print(f"非空块数量: {non_empty_blocks} / {num_blocks}")
        print(f"总点数: {slice_info['metadata']['total_points']:,}")
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        print(f"输出目录: {output_dir}")
        print(f"切片信息文件: {os.path.join(output_dir, 'spatial_slice_info.json')}")
        
        return 0
    except KeyboardInterrupt:
        print("\n用户中断操作")
        return 1
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
